# SchoolAssistant 校园助手项目设计文档

## 项目概述

SchoolAssistant 是一个面向校园用户的综合服务平台，提供帖子发布、快递代取等核心功能，采用前后端分离架构。

## 技术架构

### 整体架构
- **前端**: Vue.js 3 + Element Plus + TypeScript
- **后端**: Node.js + Express + TypeScript
- **数据库**: MySQL + Redis
- **认证**: JWT Token
- **文件存储**: 本地存储 + 阿里云OSS（可选）

### 项目结构
```
SchoolAssistant/
├── frontend/           # 前端项目
│   ├── src/
│   │   ├── components/ # 公共组件
│   │   ├── views/      # 页面组件
│   │   ├── router/     # 路由配置
│   │   ├── store/      # 状态管理
│   │   ├── api/        # API接口
│   │   └── utils/      # 工具函数
├── backend/            # 后端项目
│   ├── src/
│   │   ├── controllers/# 控制器
│   │   ├── models/     # 数据模型
│   │   ├── routes/     # 路由
│   │   ├── middleware/ # 中间件
│   │   ├── services/   # 业务逻辑
│   │   └── utils/      # 工具函数
└── docs/               # 文档
```

## 核心功能模块

### 1. 用户认证模块
**功能描述**: 用户注册、登录、权限管理

**主要接口**:
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `GET /api/auth/profile` - 获取用户信息
- `PUT /api/auth/profile` - 更新用户信息

**数据模型**:
```sql
users (
  id INT PRIMARY KEY AUTO_INCREMENT,
  username VARCHAR(50) UNIQUE NOT NULL,
  email VARCHAR(100) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL,
  nickname VARCHAR(50),
  avatar VARCHAR(255),
  phone VARCHAR(20),
  student_id VARCHAR(20),
  college VARCHAR(100),
  major VARCHAR(100),
  grade INT,
  status ENUM('active', 'inactive', 'banned') DEFAULT 'active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)
```

### 2. 帖子发布模块
**功能描述**: 用户可以发布、浏览、评论、点赞帖子

**主要接口**:
- `GET /api/posts` - 获取帖子列表
- `GET /api/posts/:id` - 获取帖子详情
- `POST /api/posts` - 发布帖子
- `PUT /api/posts/:id` - 更新帖子
- `DELETE /api/posts/:id` - 删除帖子
- `POST /api/posts/:id/like` - 点赞/取消点赞
- `POST /api/posts/:id/comments` - 发表评论
- `GET /api/posts/:id/comments` - 获取评论列表

**数据模型**:
```sql
posts (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  title VARCHAR(200) NOT NULL,
  content TEXT NOT NULL,
  images JSON,
  category ENUM('学习', '生活', '交友', '求助', '其他') DEFAULT '其他',
  tags JSON,
  like_count INT DEFAULT 0,
  comment_count INT DEFAULT 0,
  view_count INT DEFAULT 0,
  status ENUM('published', 'draft', 'deleted') DEFAULT 'published',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
)

post_likes (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  post_id INT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY unique_like (user_id, post_id),
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (post_id) REFERENCES posts(id)
)

comments (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  post_id INT NOT NULL,
  parent_id INT DEFAULT NULL,
  content TEXT NOT NULL,
  like_count INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (post_id) REFERENCES posts(id),
  FOREIGN KEY (parent_id) REFERENCES comments(id)
)
```

### 3. 快递代取模块
**功能描述**: 用户可以发布代取需求，接单代取快递

**主要接口**:
- `GET /api/express` - 获取代取订单列表
- `GET /api/express/:id` - 获取订单详情
- `POST /api/express` - 发布代取需求
- `PUT /api/express/:id` - 更新订单信息
- `POST /api/express/:id/accept` - 接单
- `POST /api/express/:id/complete` - 完成订单
- `POST /api/express/:id/cancel` - 取消订单

**数据模型**:
```sql
express_orders (
  id INT PRIMARY KEY AUTO_INCREMENT,
  publisher_id INT NOT NULL,
  accepter_id INT DEFAULT NULL,
  title VARCHAR(200) NOT NULL,
  description TEXT,
  pickup_location VARCHAR(200) NOT NULL,
  delivery_location VARCHAR(200) NOT NULL,
  pickup_code VARCHAR(50),
  contact_phone VARCHAR(20) NOT NULL,
  reward_amount DECIMAL(10,2) DEFAULT 0,
  status ENUM('pending', 'accepted', 'completed', 'cancelled') DEFAULT 'pending',
  pickup_time DATETIME,
  delivery_time DATETIME,
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (publisher_id) REFERENCES users(id),
  FOREIGN KEY (accepter_id) REFERENCES users(id)
)
```

## 前端页面设计

### 主要页面
1. **登录/注册页面** (`/auth`)
2. **首页** (`/`) - 展示最新帖子和代取信息
3. **帖子广场** (`/posts`) - 帖子列表和搜索
4. **帖子详情** (`/posts/:id`) - 帖子内容和评论
5. **发布帖子** (`/posts/create`)
6. **快递代取** (`/express`) - 代取订单列表
7. **发布代取** (`/express/create`)
8. **个人中心** (`/profile`) - 用户信息和历史记录

### UI组件设计
- 响应式布局，支持移动端
- 统一的主题色彩和设计风格
- 友好的交互反馈和加载状态

## 开发计划

### 第一阶段：基础框架搭建
1. 初始化前后端项目
2. 配置开发环境和构建工具
3. 设计数据库表结构
4. 实现用户认证功能

### 第二阶段：核心功能开发
1. 实现帖子发布模块
2. 实现快递代取模块
3. 完善前端页面和交互

### 第三阶段：功能完善和优化
1. 添加搜索和筛选功能
2. 优化性能和用户体验
3. 添加消息通知功能
4. 测试和bug修复

## 安全考虑

1. **输入验证**: 所有用户输入进行严格验证
2. **SQL注入防护**: 使用参数化查询
3. **XSS防护**: 对用户输入进行转义
4. **CSRF防护**: 使用CSRF Token
5. **权限控制**: 基于JWT的身份验证和授权
6. **敏感信息保护**: 密码加密存储，敏感数据脱敏

## 部署方案

1. **开发环境**: 本地开发，使用Docker容器化
2. **测试环境**: 云服务器部署，自动化测试
3. **生产环境**: 负载均衡，数据库主从复制，Redis集群

这份设计文档涵盖了项目的主要架构和功能模块，请您查看是否有需要调整的地方。
