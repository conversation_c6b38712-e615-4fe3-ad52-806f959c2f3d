# SchoolAssistant 校园助手项目设计文档

## 项目概述

SchoolAssistant 是一个面向校园用户的综合服务平台，提供帖子发布、快递代取等核心功能，采用前后端分离架构。

## 技术架构

### 整体架构
- **前端**: Vue.js 3 + Element Plus + TypeScript
- **后端**: Java + Spring Boot + Spring Security + MyBatis-Plus
- **数据库**: MySQL + Redis
- **认证**: JWT Token + Spring Security
- **文件存储**: 本地存储 + 阿里云OSS（可选）
- **构建工具**: Maven
- **API文档**: Swagger/OpenAPI 3

### 项目结构
```
SchoolAssistant/
├── frontend/           # 前端项目
│   ├── src/
│   │   ├── components/ # 公共组件
│   │   ├── views/      # 页面组件
│   │   ├── router/     # 路由配置
│   │   ├── store/      # 状态管理
│   │   ├── api/        # API接口
│   │   └── utils/      # 工具函数
├── backend/            # 后端项目 (Spring Boot)
│   ├── src/main/java/com/schoolassistant/
│   │   ├── controller/ # 控制器层
│   │   ├── service/    # 业务逻辑层
│   │   ├── mapper/     # 数据访问层 (MyBatis)
│   │   ├── entity/     # 实体类
│   │   ├── dto/        # 数据传输对象
│   │   ├── config/     # 配置类
│   │   ├── security/   # 安全配置
│   │   ├── utils/      # 工具类
│   │   └── exception/  # 异常处理
│   ├── src/main/resources/
│   │   ├── mapper/     # MyBatis XML映射文件
│   │   ├── application.yml # 配置文件
│   │   └── static/     # 静态资源
│   └── pom.xml         # Maven配置文件
└── docs/               # 文档
```

### Java后端技术栈详细说明
- **Spring Boot 2.7+**: 快速构建微服务应用
- **Spring Security**: 安全框架，处理认证和授权
- **Spring Web**: RESTful API开发
- **MyBatis-Plus**: 数据库ORM框架，简化CRUD操作
- **MySQL Connector**: MySQL数据库连接驱动
- **Jedis/Lettuce**: Redis客户端
- **Jackson**: JSON序列化/反序列化
- **Hibernate Validator**: 参数校验
- **Swagger**: API文档生成
- **Lombok**: 简化Java代码编写
- **Logback**: 日志框架

## 核心功能模块

### 1. 用户认证模块
**功能描述**: 用户注册、登录、权限管理

**主要接口**:
- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/logout` - 用户登出
- `GET /api/v1/auth/profile` - 获取用户信息
- `PUT /api/v1/auth/profile` - 更新用户信息
- `POST /api/v1/auth/refresh` - 刷新Token

**数据模型**:
```sql
users (
  id INT PRIMARY KEY AUTO_INCREMENT,
  username VARCHAR(50) UNIQUE NOT NULL,
  email VARCHAR(100) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL,
  nickname VARCHAR(50),
  avatar VARCHAR(255),
  phone VARCHAR(20),
  student_id VARCHAR(20),
  college VARCHAR(100),
  major VARCHAR(100),
  grade INT,
  status ENUM('active', 'inactive', 'banned') DEFAULT 'active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)
```

### 2. 帖子发布模块
**功能描述**: 用户可以发布、浏览、评论、点赞帖子

**主要接口**:
- `GET /api/v1/posts` - 获取帖子列表 (支持分页、筛选)
- `GET /api/v1/posts/{id}` - 获取帖子详情
- `POST /api/v1/posts` - 发布帖子
- `PUT /api/v1/posts/{id}` - 更新帖子
- `DELETE /api/v1/posts/{id}` - 删除帖子
- `POST /api/v1/posts/{id}/like` - 点赞/取消点赞
- `POST /api/v1/posts/{id}/comments` - 发表评论
- `GET /api/v1/posts/{id}/comments` - 获取评论列表
- `POST /api/v1/posts/upload` - 上传图片

**数据模型**:
```sql
posts (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  title VARCHAR(200) NOT NULL,
  content TEXT NOT NULL,
  images JSON,
  category ENUM('学习', '生活', '交友', '求助', '其他') DEFAULT '其他',
  tags JSON,
  like_count INT DEFAULT 0,
  comment_count INT DEFAULT 0,
  view_count INT DEFAULT 0,
  status ENUM('published', 'draft', 'deleted') DEFAULT 'published',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
)

post_likes (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  post_id INT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY unique_like (user_id, post_id),
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (post_id) REFERENCES posts(id)
)

comments (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  post_id INT NOT NULL,
  parent_id INT DEFAULT NULL,
  content TEXT NOT NULL,
  like_count INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (post_id) REFERENCES posts(id),
  FOREIGN KEY (parent_id) REFERENCES comments(id)
)
```

### 3. 快递代取模块
**功能描述**: 用户可以发布代取需求，接单代取快递

**主要接口**:
- `GET /api/v1/express` - 获取代取订单列表 (支持分页、状态筛选)
- `GET /api/v1/express/{id}` - 获取订单详情
- `POST /api/v1/express` - 发布代取需求
- `PUT /api/v1/express/{id}` - 更新订单信息
- `POST /api/v1/express/{id}/accept` - 接单
- `POST /api/v1/express/{id}/complete` - 完成订单
- `POST /api/v1/express/{id}/cancel` - 取消订单
- `GET /api/v1/express/my/published` - 获取我发布的订单
- `GET /api/v1/express/my/accepted` - 获取我接受的订单

**数据模型**:
```sql
express_orders (
  id INT PRIMARY KEY AUTO_INCREMENT,
  publisher_id INT NOT NULL,
  accepter_id INT DEFAULT NULL,
  title VARCHAR(200) NOT NULL,
  description TEXT,
  pickup_location VARCHAR(200) NOT NULL,
  delivery_location VARCHAR(200) NOT NULL,
  pickup_code VARCHAR(50),
  contact_phone VARCHAR(20) NOT NULL,
  reward_amount DECIMAL(10,2) DEFAULT 0,
  status ENUM('pending', 'accepted', 'completed', 'cancelled') DEFAULT 'pending',
  pickup_time DATETIME,
  delivery_time DATETIME,
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (publisher_id) REFERENCES users(id),
  FOREIGN KEY (accepter_id) REFERENCES users(id)
)
```

## 前端页面设计

### 主要页面
1. **登录/注册页面** (`/auth`)
2. **首页** (`/`) - 展示最新帖子和代取信息
3. **帖子广场** (`/posts`) - 帖子列表和搜索
4. **帖子详情** (`/posts/:id`) - 帖子内容和评论
5. **发布帖子** (`/posts/create`)
6. **快递代取** (`/express`) - 代取订单列表
7. **发布代取** (`/express/create`)
8. **个人中心** (`/profile`) - 用户信息和历史记录

### UI组件设计
- 响应式布局，支持移动端
- 统一的主题色彩和设计风格
- 友好的交互反馈和加载状态

## 开发计划

### 第一阶段：基础框架搭建
1. 初始化前后端项目
2. 配置开发环境和构建工具
3. 设计数据库表结构
4. 实现用户认证功能

### 第二阶段：核心功能开发
1. 实现帖子发布模块
2. 实现快递代取模块
3. 完善前端页面和交互

### 第三阶段：功能完善和优化
1. 添加搜索和筛选功能
2. 优化性能和用户体验
3. 添加消息通知功能
4. 测试和bug修复

## 安全考虑

1. **输入验证**: 所有用户输入进行严格验证
2. **SQL注入防护**: 使用参数化查询
3. **XSS防护**: 对用户输入进行转义
4. **CSRF防护**: 使用CSRF Token
5. **权限控制**: 基于JWT的身份验证和授权
6. **敏感信息保护**: 密码加密存储，敏感数据脱敏

## 部署方案

1. **开发环境**: 本地开发，使用Docker容器化
2. **测试环境**: 云服务器部署，自动化测试
3. **生产环境**: 负载均衡，数据库主从复制，Redis集群

## Java后端依赖配置 (pom.xml)

```xml
<dependencies>
    <!-- Spring Boot Starter -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
    </dependency>

    <!-- Spring Security -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-security</artifactId>
    </dependency>

    <!-- MyBatis Plus -->
    <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-boot-starter</artifactId>
        <version>3.5.3</version>
    </dependency>

    <!-- MySQL Driver -->
    <dependency>
        <groupId>mysql</groupId>
        <artifactId>mysql-connector-java</artifactId>
    </dependency>

    <!-- Redis -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-redis</artifactId>
    </dependency>

    <!-- JWT -->
    <dependency>
        <groupId>io.jsonwebtoken</groupId>
        <artifactId>jjwt-api</artifactId>
        <version>0.11.5</version>
    </dependency>

    <!-- Swagger -->
    <dependency>
        <groupId>io.springfox</groupId>
        <artifactId>springfox-boot-starter</artifactId>
        <version>3.0.0</version>
    </dependency>

    <!-- Lombok -->
    <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <optional>true</optional>
    </dependency>

    <!-- Validation -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-validation</artifactId>
    </dependency>
</dependencies>
```

这份设计文档涵盖了项目的主要架构和功能模块，现在已更新为Java后端技术栈，请您查看是否有需要调整的地方。
