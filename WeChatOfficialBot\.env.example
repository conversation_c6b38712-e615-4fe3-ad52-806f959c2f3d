# 服务器配置
PORT=3001
NODE_ENV=development

# 微信公众号配置
WECHAT_TOKEN=your_wechat_token_here
WECHAT_APPID=your_app_id_here
WECHAT_APPSECRET=your_app_secret_here
WECHAT_ENCODING_AES_KEY=your_encoding_aes_key_here

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password_here
DB_NAME=wechat_bot

# 激活码数据库配置（如果使用外部数据库）
ACTIVATION_DB_HOST=localhost
ACTIVATION_DB_PORT=3306
ACTIVATION_DB_USER=root
ACTIVATION_DB_PASSWORD=your_password_here
ACTIVATION_DB_NAME=activation_codes

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log

# 安全配置
JWT_SECRET=your_jwt_secret_here
ENCRYPTION_KEY=your_encryption_key_here

# 业务配置
MAX_ACTIVATION_CODES_PER_USER=1
DEFAULT_REPLY_MESSAGE=欢迎关注我们的公众号！
ACTIVATION_CODE_KEYWORD=emo

# Redis配置（可选，用于缓存）
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# 微信API配置
WECHAT_API_BASE_URL=https://api.weixin.qq.com
ACCESS_TOKEN_CACHE_TIME=7200
