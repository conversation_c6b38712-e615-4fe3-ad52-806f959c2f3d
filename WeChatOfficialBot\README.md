# 微信公众号开发项目

基于微信官方开发文档构建的标准微信公众号开发项目。

## 📋 项目概述

本项目严格按照微信官方开发文档标准开发，实现以下核心功能：

### 🔧 服务器对接
- **URL验证**: 微信服务器验证开发者服务器
- **消息接收**: 接收用户发送的各类消息
- **事件推送**: 处理关注、取消关注等事件
- **被动回复**: 自动回复用户消息

### 🎯 业务功能
- **激活码管理**: 关键词触发激活码分发
- **用户管理**: 跟踪用户关注状态
- **消息处理**: 智能消息分发和处理

## 🏗️ 项目结构

```
WeChatOfficialBot/
├── src/
│   ├── app.js              # 主应用入口
│   ├── config/             # 配置文件
│   │   ├── index.js        # 主配置
│   │   └── wechat.js       # 微信配置
│   ├── controllers/        # 控制器
│   │   ├── wechat.js       # 微信消息控制器
│   │   └── activation.js   # 激活码控制器
│   ├── services/           # 服务层
│   │   ├── wechat.js       # 微信服务
│   │   ├── message.js      # 消息处理服务
│   │   └── activation.js   # 激活码服务
│   ├── utils/              # 工具类
│   │   ├── crypto.js       # 加密工具
│   │   ├── xml.js          # XML处理
│   │   └── logger.js       # 日志工具
│   └── models/             # 数据模型
│       ├── user.js         # 用户模型
│       └── activation.js   # 激活码模型
├── database/               # 数据库相关
│   ├── init.sql           # 数据库初始化
│   └── connection.js      # 数据库连接
├── tests/                  # 测试文件
├── docs/                   # 文档
├── package.json
└── .env.example           # 环境变量示例
```

## 🚀 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 配置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，填入微信配置信息
```

### 3. 初始化数据库
```bash
# 执行 database/init.sql 文件
```

### 4. 启动服务
```bash
npm start
```

### 5. 配置微信公众号
在微信公众平台配置服务器URL和Token

## 📚 开发文档

- [微信公众号开发文档](https://developers.weixin.qq.com/doc/subscription/guide/dev/)
- [消息与事件推送](https://developers.weixin.qq.com/doc/subscription/guide/dev/push/)
- [服务端API调用](https://developers.weixin.qq.com/doc/subscription/guide/dev/api/)

## 🔐 安全说明

- 所有敏感信息通过环境变量配置
- 实现消息签名验证
- 支持消息加解密（可选）

## 📝 更新日志

### v1.0.0
- 初始版本
- 实现基础服务器验证
- 支持消息接收和被动回复
- 集成激活码管理功能
