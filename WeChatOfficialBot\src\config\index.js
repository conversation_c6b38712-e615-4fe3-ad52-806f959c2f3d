/**
 * 主配置文件
 * 统一管理所有配置项
 */

require('dotenv').config();

const config = {
  // 服务器配置
  server: {
    port: process.env.PORT || 3001,
    env: process.env.NODE_ENV || 'development',
    host: process.env.HOST || '0.0.0.0'
  },

  // 微信公众号配置
  wechat: {
    token: process.env.WECHAT_TOKEN || '',
    appId: process.env.WECHAT_APPID || '',
    appSecret: process.env.WECHAT_APPSECRET || '',
    encodingAESKey: process.env.WECHAT_ENCODING_AES_KEY || '',
    
    // API配置
    apiBaseUrl: process.env.WECHAT_API_BASE_URL || 'https://api.weixin.qq.com',
    accessTokenCacheTime: parseInt(process.env.ACCESS_TOKEN_CACHE_TIME) || 7200,
    
    // 消息配置
    messageEncryption: process.env.WECHAT_MESSAGE_ENCRYPTION === 'true',
    messageTimeout: parseInt(process.env.WECHAT_MESSAGE_TIMEOUT) || 5000
  },

  // 数据库配置
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT) || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'wechat_bot',
    charset: 'utf8mb4',
    timezone: '+08:00',
    
    // 连接池配置
    connectionLimit: 10,
    acquireTimeout: 60000,
    timeout: 60000,
    reconnect: true
  },

  // 激活码数据库配置
  activationDatabase: {
    host: process.env.ACTIVATION_DB_HOST || 'localhost',
    port: parseInt(process.env.ACTIVATION_DB_PORT) || 3306,
    user: process.env.ACTIVATION_DB_USER || 'root',
    password: process.env.ACTIVATION_DB_PASSWORD || '',
    database: process.env.ACTIVATION_DB_NAME || 'activation_codes',
    charset: 'utf8mb4',
    timezone: '+08:00'
  },

  // 日志配置
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    file: process.env.LOG_FILE || 'logs/app.log',
    maxSize: '10m',
    maxFiles: '7d',
    format: 'combined'
  },

  // 安全配置
  security: {
    jwtSecret: process.env.JWT_SECRET || 'default-jwt-secret',
    encryptionKey: process.env.ENCRYPTION_KEY || 'default-encryption-key',
    corsOrigin: process.env.CORS_ORIGIN || '*',
    rateLimitWindow: 15 * 60 * 1000, // 15分钟
    rateLimitMax: 100 // 每个IP每15分钟最多100次请求
  },

  // 业务配置
  business: {
    maxActivationCodesPerUser: parseInt(process.env.MAX_ACTIVATION_CODES_PER_USER) || 1,
    defaultReplyMessage: process.env.DEFAULT_REPLY_MESSAGE || '欢迎关注我们的公众号！',
    activationCodeKeyword: process.env.ACTIVATION_CODE_KEYWORD || 'emo',
    
    // 激活码类型过滤
    allowedCodeTypes: ['1day_vip'],
    
    // 用户状态
    userStatus: {
      FOLLOWED: 1,
      UNFOLLOWED: 0
    }
  },

  // Redis配置（可选）
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT) || 6379,
    password: process.env.REDIS_PASSWORD || '',
    db: parseInt(process.env.REDIS_DB) || 0,
    keyPrefix: 'wechat_bot:'
  }
};

// 配置验证
function validateConfig() {
  const required = [
    'wechat.token',
    'wechat.appId',
    'wechat.appSecret'
  ];

  for (const key of required) {
    const value = key.split('.').reduce((obj, k) => obj && obj[k], config);
    if (!value) {
      throw new Error(`Missing required configuration: ${key}`);
    }
  }
}

// 开发环境下验证配置
if (config.server.env === 'development') {
  try {
    validateConfig();
    console.log('✅ 配置验证通过');
  } catch (error) {
    console.warn('⚠️ 配置验证警告:', error.message);
  }
}

module.exports = config;
