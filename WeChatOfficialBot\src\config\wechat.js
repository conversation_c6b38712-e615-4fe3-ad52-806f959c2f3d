/**
 * 微信公众号专用配置
 * 包含微信API相关的所有配置和常量
 */

const config = require('./index');

// 微信API端点
const WECHAT_API = {
  // 基础API
  TOKEN_URL: 'https://api.weixin.qq.com/cgi-bin/token',
  
  // 用户管理API
  USER_INFO_URL: 'https://api.weixin.qq.com/cgi-bin/user/info',
  USER_LIST_URL: 'https://api.weixin.qq.com/cgi-bin/user/get',
  
  // 消息发送API
  SEND_MESSAGE_URL: 'https://api.weixin.qq.com/cgi-bin/message/custom/send',
  
  // 菜单管理API
  MENU_CREATE_URL: 'https://api.weixin.qq.com/cgi-bin/menu/create',
  MENU_GET_URL: 'https://api.weixin.qq.com/cgi-bin/menu/get',
  MENU_DELETE_URL: 'https://api.weixin.qq.com/cgi-bin/menu/delete',
  
  // 素材管理API
  MEDIA_UPLOAD_URL: 'https://api.weixin.qq.com/cgi-bin/media/upload',
  MEDIA_GET_URL: 'https://api.weixin.qq.com/cgi-bin/media/get'
};

// 消息类型常量
const MESSAGE_TYPES = {
  // 接收消息类型
  RECEIVE: {
    TEXT: 'text',
    IMAGE: 'image',
    VOICE: 'voice',
    VIDEO: 'video',
    SHORTVIDEO: 'shortvideo',
    LOCATION: 'location',
    LINK: 'link'
  },
  
  // 发送消息类型
  SEND: {
    TEXT: 'text',
    IMAGE: 'image',
    VOICE: 'voice',
    VIDEO: 'video',
    MUSIC: 'music',
    NEWS: 'news'
  }
};

// 事件类型常量
const EVENT_TYPES = {
  SUBSCRIBE: 'subscribe',           // 关注
  UNSUBSCRIBE: 'unsubscribe',       // 取消关注
  SCAN: 'SCAN',                     // 扫描二维码
  LOCATION: 'LOCATION',             // 上报地理位置
  CLICK: 'CLICK',                   // 菜单点击
  VIEW: 'VIEW'                      // 菜单跳转链接
};

// 错误码常量
const ERROR_CODES = {
  SUCCESS: 0,
  INVALID_CREDENTIAL: 40001,
  ACCESS_TOKEN_EXPIRED: 42001,
  API_UNAUTHORIZED: 48001,
  API_FORBIDDEN: 48004,
  API_QUOTA_EXCEEDED: 45009
};

// 微信配置类
class WeChatConfig {
  constructor() {
    this.token = config.wechat.token;
    this.appId = config.wechat.appId;
    this.appSecret = config.wechat.appSecret;
    this.encodingAESKey = config.wechat.encodingAESKey;
    this.messageEncryption = config.wechat.messageEncryption;
    this.messageTimeout = config.wechat.messageTimeout;
  }

  // 获取API URL
  getApiUrl(endpoint) {
    return WECHAT_API[endpoint] || endpoint;
  }

  // 验证配置完整性
  validate() {
    const required = ['token', 'appId', 'appSecret'];
    const missing = required.filter(key => !this[key]);
    
    if (missing.length > 0) {
      throw new Error(`微信配置缺失: ${missing.join(', ')}`);
    }
    
    return true;
  }

  // 获取Token URL
  getTokenUrl() {
    return `${WECHAT_API.TOKEN_URL}?grant_type=client_credential&appid=${this.appId}&secret=${this.appSecret}`;
  }

  // 检查是否启用消息加密
  isEncryptionEnabled() {
    return this.messageEncryption && this.encodingAESKey;
  }
}

// 消息模板
const MESSAGE_TEMPLATES = {
  // 欢迎消息
  WELCOME: {
    type: MESSAGE_TYPES.SEND.TEXT,
    content: '欢迎关注我们的公众号！回复"emo"获取激活码。'
  },
  
  // 默认回复
  DEFAULT: {
    type: MESSAGE_TYPES.SEND.TEXT,
    content: '感谢您的消息，我们会尽快回复您。'
  },
  
  // 激活码回复模板
  ACTIVATION_CODE: {
    type: MESSAGE_TYPES.SEND.TEXT,
    template: '🎉 您的激活码：{code}\n\n使用说明：\n1. 复制激活码\n2. 在应用中输入激活码\n3. 享受VIP服务\n\n注意：每个用户仅限获取一次激活码。'
  },
  
  // 已领取提示
  ALREADY_CLAIMED: {
    type: MESSAGE_TYPES.SEND.TEXT,
    content: '您已经领取过激活码了，每个用户仅限领取一次。'
  },
  
  // 无可用激活码
  NO_CODES_AVAILABLE: {
    type: MESSAGE_TYPES.SEND.TEXT,
    content: '抱歉，当前没有可用的激活码，请稍后再试。'
  }
};

// 业务规则配置
const BUSINESS_RULES = {
  // 激活码相关
  ACTIVATION: {
    KEYWORD: config.business.activationCodeKeyword,
    MAX_PER_USER: config.business.maxActivationCodesPerUser,
    ALLOWED_TYPES: config.business.allowedCodeTypes,
    REPLY_DELAY: 1000 // 回复延迟（毫秒）
  },
  
  // 用户管理
  USER: {
    STATUS: config.business.userStatus,
    AUTO_REPLY_ON_FOLLOW: true,
    TRACK_LOCATION: false
  },
  
  // 消息处理
  MESSAGE: {
    MAX_LENGTH: 2048,
    ENABLE_KEYWORD_MATCHING: true,
    CASE_SENSITIVE: false,
    TRIM_WHITESPACE: true
  }
};

module.exports = {
  WeChatConfig,
  WECHAT_API,
  MESSAGE_TYPES,
  EVENT_TYPES,
  ERROR_CODES,
  MESSAGE_TEMPLATES,
  BUSINESS_RULES
};
