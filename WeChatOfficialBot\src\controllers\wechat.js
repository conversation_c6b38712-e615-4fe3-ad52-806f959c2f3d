/**
 * 微信控制器
 * 处理微信公众号的HTTP请求
 */

const WeChatService = require('../services/wechat');
const Logger = require('../utils/logger');

class WeChatController {
  constructor() {
    this.wechatService = new WeChatService();
  }

  /**
   * 处理微信服务器验证（GET请求）
   * 用于微信公众号开发者服务器验证
   * @param {Object} req - Express请求对象
   * @param {Object} res - Express响应对象
   */
  async verify(req, res) {
    try {
      const { signature, timestamp, nonce, echostr } = req.query;
      
      Logger.wechat('verify', '收到微信验证请求', {
        signature: signature?.substring(0, 10) + '...',
        timestamp,
        nonce,
        echostr: echostr?.substring(0, 10) + '...',
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      // 参数验证
      if (!signature || !timestamp || !nonce || !echostr) {
        Logger.warn('微信验证参数不完整', { signature, timestamp, nonce, echostr });
        return res.status(400).send('参数不完整');
      }

      // 验证签名
      const isValid = this.wechatService.verifySignature({
        signature,
        timestamp,
        nonce
      });

      if (isValid) {
        Logger.wechat('verify', '✅ 微信验证成功');
        res.send(echostr);
      } else {
        Logger.wechat('verify', '❌ 微信验证失败 - 签名不匹配');
        res.status(403).send('验证失败');
      }

    } catch (error) {
      Logger.error('微信验证异常', { 
        error: error.message,
        stack: error.stack 
      });
      res.status(500).send('服务器错误');
    }
  }

  /**
   * 处理微信消息推送（POST请求）
   * 接收并处理用户发送的消息和事件
   * @param {Object} req - Express请求对象
   * @param {Object} res - Express响应对象
   */
  async handleMessage(req, res) {
    try {
      const { signature, timestamp, nonce } = req.query;
      const xmlData = req.body;

      Logger.wechat('message', '收到微信消息推送', {
        signature: signature?.substring(0, 10) + '...',
        timestamp,
        nonce,
        bodyLength: xmlData?.length,
        contentType: req.get('Content-Type')
      });

      // 验证请求来源
      const isValid = this.wechatService.verifySignature({
        signature,
        timestamp,
        nonce
      });

      if (!isValid) {
        Logger.warn('微信消息推送验证失败', { signature, timestamp, nonce });
        return res.status(403).send('验证失败');
      }

      // 检查消息体
      if (!xmlData || typeof xmlData !== 'string') {
        Logger.warn('无效的消息体', { xmlData: typeof xmlData });
        return res.status(400).send('无效的消息体');
      }

      // 处理消息
      const startTime = Date.now();
      const replyXml = await this.wechatService.handleMessage(xmlData);
      const duration = Date.now() - startTime;

      Logger.performance('handleMessage', duration);

      // 设置响应头
      res.set({
        'Content-Type': 'application/xml; charset=utf-8',
        'Cache-Control': 'no-cache'
      });

      // 返回回复消息
      if (replyXml) {
        Logger.wechat('reply', '发送回复消息', { 
          replyLength: replyXml.length,
          duration: `${duration}ms`
        });
        res.send(replyXml);
      } else {
        Logger.debug('无需回复消息');
        res.send('success');
      }

    } catch (error) {
      Logger.error('处理微信消息异常', { 
        error: error.message,
        stack: error.stack 
      });
      
      // 返回空响应，避免微信重复推送
      res.send('success');
    }
  }

  /**
   * 健康检查接口
   * @param {Object} req - Express请求对象
   * @param {Object} res - Express响应对象
   */
  async health(req, res) {
    try {
      const healthInfo = {
        status: 'ok',
        timestamp: new Date().toISOString(),
        service: 'wechat-official-bot',
        version: '1.0.0',
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        env: process.env.NODE_ENV || 'development'
      };

      res.json(healthInfo);
    } catch (error) {
      Logger.error('健康检查失败', { error: error.message });
      res.status(500).json({
        status: 'error',
        message: error.message
      });
    }
  }

  /**
   * 获取微信配置信息（开发调试用）
   * @param {Object} req - Express请求对象
   * @param {Object} res - Express响应对象
   */
  async getConfig(req, res) {
    try {
      // 只在开发环境下提供配置信息
      if (process.env.NODE_ENV === 'production') {
        return res.status(403).json({
          error: '生产环境下不允许访问配置信息'
        });
      }

      const config = {
        hasToken: !!this.wechatService.config.token,
        hasAppId: !!this.wechatService.config.appId,
        hasAppSecret: !!this.wechatService.config.appSecret,
        hasEncodingAESKey: !!this.wechatService.config.encodingAESKey,
        messageEncryption: this.wechatService.config.messageEncryption,
        messageTimeout: this.wechatService.config.messageTimeout
      };

      res.json(config);
    } catch (error) {
      Logger.error('获取配置信息失败', { error: error.message });
      res.status(500).json({
        error: error.message
      });
    }
  }

  /**
   * 测试接口（开发调试用）
   * @param {Object} req - Express请求对象
   * @param {Object} res - Express响应对象
   */
  async test(req, res) {
    try {
      // 只在开发环境下提供测试接口
      if (process.env.NODE_ENV === 'production') {
        return res.status(403).json({
          error: '生产环境下不允许访问测试接口'
        });
      }

      const { action } = req.query;

      switch (action) {
        case 'signature':
          return this.testSignature(req, res);
        case 'message':
          return this.testMessage(req, res);
        default:
          res.json({
            message: '微信服务测试接口',
            availableActions: ['signature', 'message'],
            usage: '/wechat/test?action=signature&timestamp=123&nonce=abc&signature=def'
          });
      }
    } catch (error) {
      Logger.error('测试接口异常', { error: error.message });
      res.status(500).json({
        error: error.message
      });
    }
  }

  /**
   * 测试签名验证
   * @param {Object} req - Express请求对象
   * @param {Object} res - Express响应对象
   */
  testSignature(req, res) {
    const { timestamp, nonce, signature } = req.query;
    
    if (!timestamp || !nonce) {
      return res.status(400).json({
        error: '缺少timestamp或nonce参数'
      });
    }

    const isValid = this.wechatService.verifySignature({
      signature,
      timestamp,
      nonce
    });

    res.json({
      valid: isValid,
      timestamp,
      nonce,
      signature,
      token: this.wechatService.config.token ? '已配置' : '未配置'
    });
  }

  /**
   * 测试消息处理
   * @param {Object} req - Express请求对象
   * @param {Object} res - Express响应对象
   */
  async testMessage(req, res) {
    const testXml = `
      <xml>
        <ToUserName><![CDATA[test_to]]></ToUserName>
        <FromUserName><![CDATA[test_from]]></FromUserName>
        <CreateTime>1234567890</CreateTime>
        <MsgType><![CDATA[text]]></MsgType>
        <Content><![CDATA[test message]]></Content>
        <MsgId>1234567890</MsgId>
      </xml>
    `;

    try {
      const replyXml = await this.wechatService.handleMessage(testXml);
      res.set('Content-Type', 'application/xml');
      res.send(replyXml);
    } catch (error) {
      res.status(500).json({
        error: error.message
      });
    }
  }
}

module.exports = WeChatController;
