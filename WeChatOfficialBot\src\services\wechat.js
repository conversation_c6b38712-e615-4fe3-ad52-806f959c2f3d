/**
 * 微信服务类
 * 处理微信公众号相关的核心业务逻辑
 */

const CryptoUtil = require('../utils/crypto');
const XMLUtil = require('../utils/xml');
const Logger = require('../utils/logger');
const { WeChatConfig, MESSAGE_TYPES, EVENT_TYPES, ERROR_CODES } = require('../config/wechat');

class WeChatService {
  constructor() {
    this.config = new WeChatConfig();
    this.accessToken = null;
    this.accessTokenExpireTime = 0;
    
    // 验证配置
    try {
      this.config.validate();
      Logger.info('微信服务初始化成功');
    } catch (error) {
      Logger.error('微信服务初始化失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 验证微信服务器签名
   * @param {Object} params - 验证参数
   * @returns {boolean} 验证结果
   */
  verifySignature(params) {
    const { signature, timestamp, nonce } = params;
    
    try {
      Logger.debug('开始验证微信签名', {
        signature: signature?.substring(0, 10) + '...',
        timestamp,
        nonce
      });

      const validation = CryptoUtil.validateWeChatMessage({
        signature,
        timestamp,
        nonce,
        token: this.config.token
      });

      if (validation.valid) {
        Logger.wechat('signature', '签名验证成功', { timeDiff: validation.timeDiff });
      } else {
        Logger.wechat('signature', '签名验证失败', { error: validation.error });
      }

      return validation.valid;
    } catch (error) {
      Logger.error('签名验证异常', { error: error.message });
      return false;
    }
  }

  /**
   * 处理微信消息
   * @param {string} xmlData - XML格式的消息数据
   * @returns {Promise<string>} 回复消息的XML
   */
  async handleMessage(xmlData) {
    try {
      Logger.debug('开始处理微信消息', { xmlLength: xmlData?.length });

      // 解析XML消息
      const message = await XMLUtil.parseXML(xmlData);
      
      if (!XMLUtil.validateMessage(message)) {
        Logger.warn('无效的消息格式', { message });
        return this.createDefaultReply(message);
      }

      Logger.wechat('message', '收到消息', {
        msgType: message.msgtype,
        fromUser: message.fromusername?.substring(0, 8) + '...',
        toUser: message.tousername?.substring(0, 8) + '...'
      });

      // 根据消息类型处理
      if (XMLUtil.isEventMessage(message)) {
        return await this.handleEvent(message);
      } else {
        return await this.handleTextMessage(message);
      }

    } catch (error) {
      Logger.error('处理微信消息失败', { error: error.message });
      return this.createErrorReply();
    }
  }

  /**
   * 处理事件消息
   * @param {Object} message - 消息对象
   * @returns {Promise<string>} 回复消息的XML
   */
  async handleEvent(message) {
    const eventType = XMLUtil.getEventType(message);
    
    Logger.wechat('event', `收到事件: ${eventType}`, {
      fromUser: message.fromusername?.substring(0, 8) + '...'
    });

    switch (eventType) {
      case EVENT_TYPES.SUBSCRIBE:
        return this.handleSubscribeEvent(message);
      
      case EVENT_TYPES.UNSUBSCRIBE:
        return this.handleUnsubscribeEvent(message);
      
      case EVENT_TYPES.SCAN:
        return this.handleScanEvent(message);
      
      case EVENT_TYPES.CLICK:
        return this.handleClickEvent(message);
      
      default:
        Logger.debug('未处理的事件类型', { eventType });
        return this.createDefaultReply(message);
    }
  }

  /**
   * 处理关注事件
   * @param {Object} message - 消息对象
   * @returns {string} 回复消息的XML
   */
  handleSubscribeEvent(message) {
    Logger.user(message.fromusername, '用户关注');
    
    const replyContent = '🎉 欢迎关注我们的公众号！\n\n回复 "emo" 获取激活码\n\n感谢您的关注！';
    
    return XMLUtil.createTextMessage({
      toUserName: message.fromusername,
      fromUserName: message.tousername,
      content: replyContent
    });
  }

  /**
   * 处理取消关注事件
   * @param {Object} message - 消息对象
   * @returns {string} 空字符串（取消关注无需回复）
   */
  handleUnsubscribeEvent(message) {
    Logger.user(message.fromusername, '用户取消关注');
    
    // 取消关注事件无需回复
    return '';
  }

  /**
   * 处理扫码事件
   * @param {Object} message - 消息对象
   * @returns {string} 回复消息的XML
   */
  handleScanEvent(message) {
    Logger.user(message.fromusername, '用户扫码', { eventKey: message.eventkey });
    
    return XMLUtil.createTextMessage({
      toUserName: message.fromusername,
      fromUserName: message.tousername,
      content: '感谢您的扫码！'
    });
  }

  /**
   * 处理菜单点击事件
   * @param {Object} message - 消息对象
   * @returns {string} 回复消息的XML
   */
  handleClickEvent(message) {
    const eventKey = message.eventkey;
    Logger.user(message.fromusername, '菜单点击', { eventKey });
    
    return XMLUtil.createTextMessage({
      toUserName: message.fromusername,
      fromUserName: message.tousername,
      content: `您点击了菜单: ${eventKey}`
    });
  }

  /**
   * 处理文本消息
   * @param {Object} message - 消息对象
   * @returns {Promise<string>} 回复消息的XML
   */
  async handleTextMessage(message) {
    const content = message.content?.trim().toLowerCase();
    
    Logger.user(message.fromusername, '发送文本消息', { 
      content: content?.substring(0, 50) + (content?.length > 50 ? '...' : '')
    });

    // 这里会在后续集成激活码服务
    // 目前先返回默认回复
    return this.createDefaultReply(message);
  }

  /**
   * 创建默认回复
   * @param {Object} message - 原始消息对象
   * @returns {string} 回复消息的XML
   */
  createDefaultReply(message) {
    const replyContent = '感谢您的消息！我们已收到并会尽快处理。\n\n回复 "emo" 获取激活码。';
    
    return XMLUtil.createTextMessage({
      toUserName: message.fromusername,
      fromUserName: message.tousername,
      content: replyContent
    });
  }

  /**
   * 创建错误回复
   * @returns {string} 错误回复的XML
   */
  createErrorReply() {
    return XMLUtil.createTextMessage({
      toUserName: 'error',
      fromUserName: 'system',
      content: '抱歉，系统暂时无法处理您的消息，请稍后再试。'
    });
  }

  /**
   * 获取访问令牌
   * @returns {Promise<string>} 访问令牌
   */
  async getAccessToken() {
    try {
      // 检查缓存的token是否有效
      if (this.accessToken && Date.now() < this.accessTokenExpireTime) {
        return this.accessToken;
      }

      // 获取新的token
      const url = this.config.getTokenUrl();
      const response = await fetch(url);
      const data = await response.json();

      if (data.errcode) {
        throw new Error(`获取访问令牌失败: ${data.errmsg}`);
      }

      this.accessToken = data.access_token;
      this.accessTokenExpireTime = Date.now() + (data.expires_in - 300) * 1000; // 提前5分钟过期

      Logger.wechat('token', '获取访问令牌成功', { 
        expiresIn: data.expires_in 
      });

      return this.accessToken;
    } catch (error) {
      Logger.error('获取访问令牌失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 发送客服消息
   * @param {string} openId - 用户OpenID
   * @param {string} content - 消息内容
   * @returns {Promise<boolean>} 发送结果
   */
  async sendCustomMessage(openId, content) {
    try {
      const accessToken = await this.getAccessToken();
      const url = `${this.config.getApiUrl('SEND_MESSAGE_URL')}?access_token=${accessToken}`;
      
      const messageData = {
        touser: openId,
        msgtype: 'text',
        text: {
          content: content
        }
      };

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(messageData)
      });

      const result = await response.json();
      
      if (result.errcode === 0) {
        Logger.wechat('send', '客服消息发送成功', { openId: openId.substring(0, 8) + '...' });
        return true;
      } else {
        Logger.error('客服消息发送失败', { errcode: result.errcode, errmsg: result.errmsg });
        return false;
      }
    } catch (error) {
      Logger.error('发送客服消息异常', { error: error.message });
      return false;
    }
  }
}

module.exports = WeChatService;
