/**
 * 加密工具类
 * 实现微信公众号开发中需要的各种加密功能
 */

const crypto = require('crypto');

class CryptoUtil {
  /**
   * SHA1签名验证（微信服务器验证）
   * @param {string} token - 微信Token
   * @param {string} timestamp - 时间戳
   * @param {string} nonce - 随机数
   * @param {string} signature - 微信签名
   * @returns {boolean} 验证结果
   */
  static verifySignature(token, timestamp, nonce, signature) {
    try {
      // 1. 将token、timestamp、nonce三个参数进行字典序排序
      const arr = [token, timestamp, nonce].sort();
      
      // 2. 将三个参数字符串拼接成一个字符串进行sha1加密
      const str = arr.join('');
      const sha1 = crypto.createHash('sha1');
      sha1.update(str);
      const result = sha1.digest('hex');
      
      // 3. 开发者获得加密后的字符串可与signature对比，标识该请求来源于微信
      return result === signature;
    } catch (error) {
      console.error('签名验证失败:', error);
      return false;
    }
  }

  /**
   * 生成SHA1签名
   * @param {string} token - 微信Token
   * @param {string} timestamp - 时间戳
   * @param {string} nonce - 随机数
   * @returns {string} 生成的签名
   */
  static generateSignature(token, timestamp, nonce) {
    try {
      const arr = [token, timestamp, nonce].sort();
      const str = arr.join('');
      const sha1 = crypto.createHash('sha1');
      sha1.update(str);
      return sha1.digest('hex');
    } catch (error) {
      console.error('签名生成失败:', error);
      return '';
    }
  }

  /**
   * MD5加密
   * @param {string} str - 待加密字符串
   * @returns {string} MD5加密结果
   */
  static md5(str) {
    try {
      const md5 = crypto.createHash('md5');
      md5.update(str);
      return md5.digest('hex');
    } catch (error) {
      console.error('MD5加密失败:', error);
      return '';
    }
  }

  /**
   * Base64编码
   * @param {string} str - 待编码字符串
   * @returns {string} Base64编码结果
   */
  static base64Encode(str) {
    try {
      return Buffer.from(str, 'utf8').toString('base64');
    } catch (error) {
      console.error('Base64编码失败:', error);
      return '';
    }
  }

  /**
   * Base64解码
   * @param {string} str - 待解码字符串
   * @returns {string} Base64解码结果
   */
  static base64Decode(str) {
    try {
      return Buffer.from(str, 'base64').toString('utf8');
    } catch (error) {
      console.error('Base64解码失败:', error);
      return '';
    }
  }

  /**
   * AES加密（用于消息加解密）
   * @param {string} text - 待加密文本
   * @param {string} key - 加密密钥
   * @param {string} iv - 初始化向量
   * @returns {string} 加密结果
   */
  static aesEncrypt(text, key, iv) {
    try {
      const cipher = crypto.createCipher('aes-256-cbc', key);
      let encrypted = cipher.update(text, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      return encrypted;
    } catch (error) {
      console.error('AES加密失败:', error);
      return '';
    }
  }

  /**
   * AES解密（用于消息加解密）
   * @param {string} encryptedText - 待解密文本
   * @param {string} key - 解密密钥
   * @param {string} iv - 初始化向量
   * @returns {string} 解密结果
   */
  static aesDecrypt(encryptedText, key, iv) {
    try {
      const decipher = crypto.createDecipher('aes-256-cbc', key);
      let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      return decrypted;
    } catch (error) {
      console.error('AES解密失败:', error);
      return '';
    }
  }

  /**
   * 生成随机字符串
   * @param {number} length - 字符串长度
   * @returns {string} 随机字符串
   */
  static generateRandomString(length = 16) {
    try {
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
      let result = '';
      for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
      }
      return result;
    } catch (error) {
      console.error('随机字符串生成失败:', error);
      return '';
    }
  }

  /**
   * 生成时间戳
   * @returns {string} 当前时间戳
   */
  static generateTimestamp() {
    return Math.floor(Date.now() / 1000).toString();
  }

  /**
   * 生成随机数（用于微信验证）
   * @returns {string} 随机数
   */
  static generateNonce() {
    return this.generateRandomString(16);
  }

  /**
   * 验证微信消息的完整性
   * @param {Object} params - 验证参数
   * @param {string} params.signature - 微信签名
   * @param {string} params.timestamp - 时间戳
   * @param {string} params.nonce - 随机数
   * @param {string} params.token - 微信Token
   * @returns {Object} 验证结果
   */
  static validateWeChatMessage(params) {
    const { signature, timestamp, nonce, token } = params;
    
    // 参数检查
    if (!signature || !timestamp || !nonce || !token) {
      return {
        valid: false,
        error: '缺少必要参数'
      };
    }

    // 时间戳检查（防止重放攻击）
    const currentTime = Math.floor(Date.now() / 1000);
    const messageTime = parseInt(timestamp);
    const timeDiff = Math.abs(currentTime - messageTime);
    
    // 允许5分钟的时间差
    if (timeDiff > 300) {
      return {
        valid: false,
        error: '消息时间戳过期'
      };
    }

    // 签名验证
    const isValid = this.verifySignature(token, timestamp, nonce, signature);
    
    return {
      valid: isValid,
      error: isValid ? null : '签名验证失败',
      timeDiff: timeDiff
    };
  }
}

module.exports = CryptoUtil;
