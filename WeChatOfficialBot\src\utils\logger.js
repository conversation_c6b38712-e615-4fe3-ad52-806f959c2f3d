/**
 * 日志工具类
 * 提供统一的日志记录功能
 */

const fs = require('fs');
const path = require('path');
const config = require('../config');

class Logger {
  constructor() {
    this.logLevel = config.logging.level;
    this.logFile = config.logging.file;
    this.levels = {
      error: 0,
      warn: 1,
      info: 2,
      debug: 3
    };
    
    // 确保日志目录存在
    this.ensureLogDirectory();
  }

  /**
   * 确保日志目录存在
   */
  ensureLogDirectory() {
    try {
      const logDir = path.dirname(this.logFile);
      if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
      }
    } catch (error) {
      console.error('创建日志目录失败:', error);
    }
  }

  /**
   * 格式化日志消息
   * @param {string} level - 日志级别
   * @param {string} message - 日志消息
   * @param {Object} meta - 附加信息
   * @returns {string} 格式化后的日志
   */
  formatMessage(level, message, meta = {}) {
    const timestamp = new Date().toISOString();
    const levelStr = level.toUpperCase().padEnd(5);
    
    let logMessage = `[${timestamp}] ${levelStr} ${message}`;
    
    // 添加附加信息
    if (Object.keys(meta).length > 0) {
      logMessage += ` | ${JSON.stringify(meta)}`;
    }
    
    return logMessage;
  }

  /**
   * 写入日志文件
   * @param {string} message - 日志消息
   */
  writeToFile(message) {
    try {
      fs.appendFileSync(this.logFile, message + '\n');
    } catch (error) {
      console.error('写入日志文件失败:', error);
    }
  }

  /**
   * 检查是否应该记录该级别的日志
   * @param {string} level - 日志级别
   * @returns {boolean} 是否记录
   */
  shouldLog(level) {
    const currentLevel = this.levels[this.logLevel] || 2;
    const messageLevel = this.levels[level] || 2;
    return messageLevel <= currentLevel;
  }

  /**
   * 记录错误日志
   * @param {string} message - 日志消息
   * @param {Object} meta - 附加信息
   */
  error(message, meta = {}) {
    if (!this.shouldLog('error')) return;
    
    const formattedMessage = this.formatMessage('error', message, meta);
    console.error('\x1b[31m%s\x1b[0m', formattedMessage); // 红色
    this.writeToFile(formattedMessage);
  }

  /**
   * 记录警告日志
   * @param {string} message - 日志消息
   * @param {Object} meta - 附加信息
   */
  warn(message, meta = {}) {
    if (!this.shouldLog('warn')) return;
    
    const formattedMessage = this.formatMessage('warn', message, meta);
    console.warn('\x1b[33m%s\x1b[0m', formattedMessage); // 黄色
    this.writeToFile(formattedMessage);
  }

  /**
   * 记录信息日志
   * @param {string} message - 日志消息
   * @param {Object} meta - 附加信息
   */
  info(message, meta = {}) {
    if (!this.shouldLog('info')) return;
    
    const formattedMessage = this.formatMessage('info', message, meta);
    console.info('\x1b[36m%s\x1b[0m', formattedMessage); // 青色
    this.writeToFile(formattedMessage);
  }

  /**
   * 记录调试日志
   * @param {string} message - 日志消息
   * @param {Object} meta - 附加信息
   */
  debug(message, meta = {}) {
    if (!this.shouldLog('debug')) return;
    
    const formattedMessage = this.formatMessage('debug', message, meta);
    console.debug('\x1b[37m%s\x1b[0m', formattedMessage); // 白色
    this.writeToFile(formattedMessage);
  }

  /**
   * 记录微信相关日志
   * @param {string} type - 日志类型
   * @param {string} message - 日志消息
   * @param {Object} data - 相关数据
   */
  wechat(type, message, data = {}) {
    const meta = {
      type: 'wechat',
      subType: type,
      ...data
    };
    
    this.info(`[微信] ${message}`, meta);
  }

  /**
   * 记录API调用日志
   * @param {string} method - HTTP方法
   * @param {string} url - 请求URL
   * @param {number} status - 响应状态码
   * @param {number} duration - 请求耗时
   */
  api(method, url, status, duration) {
    const meta = {
      type: 'api',
      method,
      url,
      status,
      duration: `${duration}ms`
    };
    
    const level = status >= 400 ? 'error' : 'info';
    this[level](`API ${method} ${url} - ${status}`, meta);
  }

  /**
   * 记录数据库操作日志
   * @param {string} operation - 操作类型
   * @param {string} table - 表名
   * @param {Object} data - 相关数据
   */
  database(operation, table, data = {}) {
    const meta = {
      type: 'database',
      operation,
      table,
      ...data
    };
    
    this.debug(`[数据库] ${operation} ${table}`, meta);
  }

  /**
   * 记录用户操作日志
   * @param {string} openId - 用户OpenID
   * @param {string} action - 操作类型
   * @param {Object} data - 相关数据
   */
  user(openId, action, data = {}) {
    const meta = {
      type: 'user',
      openId: openId ? openId.substring(0, 8) + '...' : 'unknown',
      action,
      ...data
    };
    
    this.info(`[用户] ${action}`, meta);
  }

  /**
   * 记录激活码相关日志
   * @param {string} action - 操作类型
   * @param {Object} data - 相关数据
   */
  activation(action, data = {}) {
    const meta = {
      type: 'activation',
      action,
      ...data
    };
    
    this.info(`[激活码] ${action}`, meta);
  }

  /**
   * 记录性能日志
   * @param {string} operation - 操作名称
   * @param {number} duration - 耗时
   * @param {Object} meta - 附加信息
   */
  performance(operation, duration, meta = {}) {
    const perfMeta = {
      type: 'performance',
      operation,
      duration: `${duration}ms`,
      ...meta
    };
    
    const level = duration > 1000 ? 'warn' : 'debug';
    this[level](`[性能] ${operation} 耗时 ${duration}ms`, perfMeta);
  }

  /**
   * 记录启动日志
   * @param {string} service - 服务名称
   * @param {Object} config - 配置信息
   */
  startup(service, config = {}) {
    const meta = {
      type: 'startup',
      service,
      ...config
    };
    
    this.info(`🚀 ${service} 启动成功`, meta);
  }

  /**
   * 记录关闭日志
   * @param {string} service - 服务名称
   * @param {string} reason - 关闭原因
   */
  shutdown(service, reason = 'normal') {
    const meta = {
      type: 'shutdown',
      service,
      reason
    };
    
    this.info(`🛑 ${service} 关闭`, meta);
  }
}

// 创建单例实例
const logger = new Logger();

module.exports = logger;
