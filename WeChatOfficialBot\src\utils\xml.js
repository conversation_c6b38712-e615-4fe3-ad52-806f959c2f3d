/**
 * XML处理工具类
 * 处理微信消息的XML格式转换
 */

const xml2js = require('xml2js');

class XMLUtil {
  constructor() {
    // XML解析器配置
    this.parseOptions = {
      explicitArray: false,
      ignoreAttrs: true,
      explicitRoot: false
    };

    // XML构建器配置
    this.buildOptions = {
      rootName: 'xml',
      headless: true,
      cdata: true
    };

    this.parser = new xml2js.Parser(this.parseOptions);
    this.builder = new xml2js.Builder(this.buildOptions);
  }

  /**
   * 解析XML字符串为JavaScript对象
   * @param {string} xmlString - XML字符串
   * @returns {Promise<Object>} 解析后的对象
   */
  async parseXML(xmlString) {
    try {
      if (!xmlString || typeof xmlString !== 'string') {
        throw new Error('无效的XML字符串');
      }

      const result = await this.parser.parseStringPromise(xmlString);
      return this.formatParsedData(result);
    } catch (error) {
      console.error('XML解析失败:', error);
      throw new Error(`XML解析失败: ${error.message}`);
    }
  }

  /**
   * 将JavaScript对象转换为XML字符串
   * @param {Object} obj - 待转换的对象
   * @returns {string} XML字符串
   */
  buildXML(obj) {
    try {
      if (!obj || typeof obj !== 'object') {
        throw new Error('无效的对象');
      }

      return this.builder.buildObject(obj);
    } catch (error) {
      console.error('XML构建失败:', error);
      throw new Error(`XML构建失败: ${error.message}`);
    }
  }

  /**
   * 格式化解析后的数据
   * @param {Object} data - 原始解析数据
   * @returns {Object} 格式化后的数据
   */
  formatParsedData(data) {
    const formatted = {};
    
    for (const [key, value] of Object.entries(data)) {
      // 转换键名为小写
      const lowerKey = key.toLowerCase();
      
      // 处理特殊字段
      if (lowerKey === 'createtime') {
        formatted[lowerKey] = parseInt(value) || 0;
      } else if (lowerKey === 'msgid') {
        formatted[lowerKey] = parseInt(value) || 0;
      } else {
        formatted[lowerKey] = value || '';
      }
    }
    
    return formatted;
  }

  /**
   * 创建文本消息XML
   * @param {Object} params - 消息参数
   * @returns {string} XML字符串
   */
  createTextMessage(params) {
    const { toUserName, fromUserName, content } = params;
    
    const message = {
      ToUserName: toUserName,
      FromUserName: fromUserName,
      CreateTime: Math.floor(Date.now() / 1000),
      MsgType: 'text',
      Content: content
    };

    return this.buildXML(message);
  }

  /**
   * 创建图片消息XML
   * @param {Object} params - 消息参数
   * @returns {string} XML字符串
   */
  createImageMessage(params) {
    const { toUserName, fromUserName, mediaId } = params;
    
    const message = {
      ToUserName: toUserName,
      FromUserName: fromUserName,
      CreateTime: Math.floor(Date.now() / 1000),
      MsgType: 'image',
      Image: {
        MediaId: mediaId
      }
    };

    return this.buildXML(message);
  }

  /**
   * 创建语音消息XML
   * @param {Object} params - 消息参数
   * @returns {string} XML字符串
   */
  createVoiceMessage(params) {
    const { toUserName, fromUserName, mediaId } = params;
    
    const message = {
      ToUserName: toUserName,
      FromUserName: fromUserName,
      CreateTime: Math.floor(Date.now() / 1000),
      MsgType: 'voice',
      Voice: {
        MediaId: mediaId
      }
    };

    return this.buildXML(message);
  }

  /**
   * 创建视频消息XML
   * @param {Object} params - 消息参数
   * @returns {string} XML字符串
   */
  createVideoMessage(params) {
    const { toUserName, fromUserName, mediaId, title, description } = params;
    
    const message = {
      ToUserName: toUserName,
      FromUserName: fromUserName,
      CreateTime: Math.floor(Date.now() / 1000),
      MsgType: 'video',
      Video: {
        MediaId: mediaId,
        Title: title || '',
        Description: description || ''
      }
    };

    return this.buildXML(message);
  }

  /**
   * 创建音乐消息XML
   * @param {Object} params - 消息参数
   * @returns {string} XML字符串
   */
  createMusicMessage(params) {
    const { toUserName, fromUserName, title, description, musicUrl, hqMusicUrl, thumbMediaId } = params;
    
    const message = {
      ToUserName: toUserName,
      FromUserName: fromUserName,
      CreateTime: Math.floor(Date.now() / 1000),
      MsgType: 'music',
      Music: {
        Title: title || '',
        Description: description || '',
        MusicUrl: musicUrl || '',
        HQMusicUrl: hqMusicUrl || '',
        ThumbMediaId: thumbMediaId || ''
      }
    };

    return this.buildXML(message);
  }

  /**
   * 创建图文消息XML
   * @param {Object} params - 消息参数
   * @returns {string} XML字符串
   */
  createNewsMessage(params) {
    const { toUserName, fromUserName, articles } = params;
    
    if (!Array.isArray(articles) || articles.length === 0) {
      throw new Error('图文消息至少需要一篇文章');
    }

    const message = {
      ToUserName: toUserName,
      FromUserName: fromUserName,
      CreateTime: Math.floor(Date.now() / 1000),
      MsgType: 'news',
      ArticleCount: articles.length,
      Articles: {
        item: articles.map(article => ({
          Title: article.title || '',
          Description: article.description || '',
          PicUrl: article.picUrl || '',
          Url: article.url || ''
        }))
      }
    };

    return this.buildXML(message);
  }

  /**
   * 验证消息格式
   * @param {Object} message - 消息对象
   * @returns {boolean} 验证结果
   */
  validateMessage(message) {
    if (!message || typeof message !== 'object') {
      return false;
    }

    const requiredFields = ['tousername', 'fromusername', 'createtime', 'msgtype'];
    return requiredFields.every(field => message.hasOwnProperty(field));
  }

  /**
   * 获取消息类型
   * @param {Object} message - 消息对象
   * @returns {string} 消息类型
   */
  getMessageType(message) {
    return message && message.msgtype ? message.msgtype.toLowerCase() : '';
  }

  /**
   * 检查是否为事件消息
   * @param {Object} message - 消息对象
   * @returns {boolean} 是否为事件消息
   */
  isEventMessage(message) {
    return this.getMessageType(message) === 'event';
  }

  /**
   * 获取事件类型
   * @param {Object} message - 消息对象
   * @returns {string} 事件类型
   */
  getEventType(message) {
    return message && message.event ? message.event.toLowerCase() : '';
  }
}

// 创建单例实例
const xmlUtil = new XMLUtil();

module.exports = xmlUtil;
